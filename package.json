{"name": "nextech-solutions", "private": true, "version": "1.0.0", "type": "module", "scripts": {"build:css": "postcss src/index.css -o _site/index.css", "build:eleventy": "eleventy --config=.eleventy.cjs", "build": "npm run build:css && npm run build:eleventy", "dev:css": "postcss src/index.css -o _site/index.css --watch", "dev:eleventy": "eleventy --serve --watch --config=.eleventy.cjs", "dev": "npm run build:css && concurrently \"npm run dev:css\" \"npm run dev:eleventy\"", "preview": "eleventy --serve --port=8080 --config=.eleventy.cjs", "clean": "rm -rf _site", "deploy": "npm run build && netlify deploy --prod --dir=_site", "debug": "DEBUG=Eleventy* eleventy --config=.eleventy.cjs"}, "devDependencies": {"@11ty/eleventy": "^2.0.0", "@11ty/eleventy-plugin-bundle": "^2.0.0", "@11ty/eleventy-plugin-rss": "^1.2.0", "@11ty/eleventy-plugin-syntaxhighlight": "^4.0.0", "autoprefixer": "^10.4.18", "concurrently": "^9.1.2", "luxon": "^3.4.4", "markdown-it": "^14.0.0", "markdown-it-anchor": "^8.6.7", "postcss": "^8.4.35", "postcss-cli": "^11.0.0", "prettier": "^3.2.4", "tailwindcss": "^3.4.1"}}