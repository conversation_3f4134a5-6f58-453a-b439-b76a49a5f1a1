---
layout: base.njk
title: Free IT Resources
description: Download free IT guides, checklists, and whitepapers. Expert insights on cloud migration, cybersecurity, digital transformation, and technology best practices.
permalink: /resources/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Free IT Resources
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Download expert guides, checklists, and whitepapers to help you make informed 
          technology decisions and improve your business operations.
        </p>
      </div>
    </div>
  </section>

  <!-- Resource Categories -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Expert Knowledge at Your Fingertips
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Access our library of practical guides and resources designed to help you navigate 
          technology challenges and opportunities.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {% set resourceStats = [
          {
            stat: "25+",
            description: "Free Resources"
          },
          {
            stat: "10,000+",
            description: "Downloads"
          },
          {
            stat: "50+",
            description: "Pages of Content"
          },
          {
            stat: "100%",
            description: "Free Access"
          }
        ] %}

        {% for stat in resourceStats %}
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">{{ stat.stat }}</div>
            <p class="text-gray-700 font-medium">{{ stat.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Featured Resources -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Featured Resources
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Our most popular and comprehensive guides to help you succeed with technology.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {% set featuredResources = [
          {
            title: "Complete Cloud Migration Guide",
            description: "Step-by-step guide to planning and executing a successful cloud migration for your business.",
            type: "Whitepaper",
            pages: "24 pages",
            image: "https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            downloadUrl: "/downloads/cloud-migration-guide.pdf",
            topics: ["Cloud Strategy", "Migration Planning", "Cost Optimization", "Risk Management"]
          },
          {
            title: "Cybersecurity Checklist for SMEs",
            description: "Essential cybersecurity measures every small and medium business should implement immediately.",
            type: "Checklist",
            pages: "8 pages",
            image: "https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            downloadUrl: "/downloads/cybersecurity-checklist.pdf",
            topics: ["Security Assessment", "Best Practices", "Compliance", "Incident Response"]
          },
          {
            title: "Digital Transformation Roadmap",
            description: "Comprehensive framework for planning and implementing digital transformation initiatives.",
            type: "Guide",
            pages: "32 pages",
            image: "https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            downloadUrl: "/downloads/digital-transformation-roadmap.pdf",
            topics: ["Strategy Development", "Technology Selection", "Change Management", "ROI Measurement"]
          }
        ] %}

        {% for resource in featuredResources %}
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="{{ resource.image }}"
                alt="{{ resource.title }}"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-4 left-4">
                <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {{ resource.type }}
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                  {{ resource.pages }}
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">{{ resource.title }}</h3>
              <p class="text-gray-600 mb-4 leading-relaxed">{{ resource.description }}</p>
              <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-2">What's Included:</h4>
                <ul class="space-y-1">
                  {% for topic in resource.topics %}
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      {{ topic }}
                    </li>
                  {% endfor %}
                </ul>
              </div>
              <button
                onclick="downloadResource('{{ resource.title }}', '{{ resource.downloadUrl }}')"
                class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center justify-center group"
              >
                Download Free
                <svg class="ml-2 w-4 h-4 group-hover:translate-y-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4"></path>
                </svg>
              </button>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- All Resources -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Complete Resource Library
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Browse our complete collection of guides, checklists, and templates organized by category.
        </p>
      </div>

      <!-- Resource Categories -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% set resourceCategories = [
          {
            category: "Cloud Solutions",
            icon: "cloud",
            resources: [
              { name: "Cloud Migration Checklist", type: "Checklist", url: "/downloads/cloud-migration-checklist.pdf" },
              { name: "Cloud Cost Optimization Guide", type: "Guide", url: "/downloads/cloud-cost-optimization.pdf" },
              { name: "Multi-Cloud Strategy Template", type: "Template", url: "/downloads/multi-cloud-strategy.pdf" },
              { name: "Cloud Security Best Practices", type: "Whitepaper", url: "/downloads/cloud-security-practices.pdf" }
            ]
          },
          {
            category: "Cybersecurity",
            icon: "shield",
            resources: [
              { name: "Security Assessment Template", type: "Template", url: "/downloads/security-assessment-template.pdf" },
              { name: "Incident Response Playbook", type: "Playbook", url: "/downloads/incident-response-playbook.pdf" },
              { name: "Employee Security Training Guide", type: "Guide", url: "/downloads/security-training-guide.pdf" },
              { name: "HIPAA Compliance Checklist", type: "Checklist", url: "/downloads/hipaa-compliance-checklist.pdf" }
            ]
          },
          {
            category: "Digital Transformation",
            icon: "transform",
            resources: [
              { name: "Digital Maturity Assessment", type: "Assessment", url: "/downloads/digital-maturity-assessment.pdf" },
              { name: "Technology ROI Calculator", type: "Tool", url: "/downloads/technology-roi-calculator.pdf" },
              { name: "Change Management Framework", type: "Framework", url: "/downloads/change-management-framework.pdf" },
              { name: "Automation Opportunity Finder", type: "Tool", url: "/downloads/automation-opportunity-finder.pdf" }
            ]
          },
          {
            category: "IT Management",
            icon: "settings",
            resources: [
              { name: "IT Budget Planning Template", type: "Template", url: "/downloads/it-budget-template.pdf" },
              { name: "Vendor Evaluation Checklist", type: "Checklist", url: "/downloads/vendor-evaluation-checklist.pdf" },
              { name: "Disaster Recovery Plan Template", type: "Template", url: "/downloads/disaster-recovery-template.pdf" },
              { name: "IT Policy Templates", type: "Templates", url: "/downloads/it-policy-templates.pdf" }
            ]
          },
          {
            category: "Industry Specific",
            icon: "industry",
            resources: [
              { name: "Healthcare IT Compliance Guide", type: "Guide", url: "/downloads/healthcare-it-compliance.pdf" },
              { name: "Financial Services Security Framework", type: "Framework", url: "/downloads/financial-security-framework.pdf" },
              { name: "Manufacturing IoT Implementation Guide", type: "Guide", url: "/downloads/manufacturing-iot-guide.pdf" },
              { name: "Retail Technology Trends Report", type: "Report", url: "/downloads/retail-tech-trends.pdf" }
            ]
          },
          {
            category: "Best Practices",
            icon: "star",
            resources: [
              { name: "Remote Work IT Setup Guide", type: "Guide", url: "/downloads/remote-work-it-setup.pdf" },
              { name: "Data Backup Best Practices", type: "Guide", url: "/downloads/data-backup-practices.pdf" },
              { name: "Network Security Checklist", type: "Checklist", url: "/downloads/network-security-checklist.pdf" },
              { name: "Software License Management Guide", type: "Guide", url: "/downloads/software-license-management.pdf" }
            ]
          }
        ] %}

        {% for category in resourceCategories %}
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                {% if category.icon == 'cloud' %}
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                  </svg>
                {% elif category.icon == 'shield' %}
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                {% elif category.icon == 'transform' %}
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                {% elif category.icon == 'settings' %}
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                {% elif category.icon == 'industry' %}
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                {% elif category.icon == 'star' %}
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                  </svg>
                {% endif %}
              </div>
              <h3 class="text-lg font-bold text-gray-900">{{ category.category }}</h3>
            </div>
            <ul class="space-y-2">
              {% for resource in category.resources %}
                <li>
                  <button
                    onclick="downloadResource('{{ resource.name }}', '{{ resource.url }}')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">{{ resource.name }}</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ resource.type }}</span>
                    </div>
                  </button>
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Newsletter Signup -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Get New Resources First
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Subscribe to our newsletter and be the first to access new guides, checklists, and industry insights.
        </p>
        <form class="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600"
            required
          >
          <button
            type="submit"
            class="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
          >
            Subscribe
          </button>
        </form>
        <p class="text-sm text-blue-100 mt-4">
          No spam, unsubscribe at any time. We respect your privacy.
        </p>
      </div>
    </div>
  </section>
</div>

<!-- Download Modal -->
<div id="downloadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
  <div class="bg-white rounded-xl p-8 max-w-md mx-4">
    <h3 class="text-2xl font-bold text-gray-900 mb-4">Download Resource</h3>
    <p class="text-gray-600 mb-6">Enter your email to download this free resource. We'll also send you updates on new resources and IT insights.</p>
    <form id="downloadForm">
      <input type="hidden" id="resourceName" name="resource">
      <input type="hidden" id="downloadUrl" name="url">
      <div class="mb-4">
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
        <input
          type="email"
          id="email"
          name="email"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="<EMAIL>"
        >
      </div>
      <div class="mb-6">
        <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company (Optional)</label>
        <input
          type="text"
          id="company"
          name="company"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Your Company"
        >
      </div>
      <div class="flex space-x-4">
        <button
          type="button"
          onclick="closeDownloadModal()"
          class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          Download
        </button>
      </div>
    </form>
  </div>
</div>

<script>
function downloadResource(name, url) {
  document.getElementById('resourceName').value = name;
  document.getElementById('downloadUrl').value = url;
  document.getElementById('downloadModal').classList.remove('hidden');
  document.getElementById('downloadModal').classList.add('flex');
}

function closeDownloadModal() {
  document.getElementById('downloadModal').classList.add('hidden');
  document.getElementById('downloadModal').classList.remove('flex');
}

document.getElementById('downloadForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const resourceName = formData.get('resource');
  const downloadUrl = formData.get('url');
  
  // Here you would typically send the form data to your server
  // For now, we'll just simulate the download
  console.log('Resource download:', resourceName, 'Email:', formData.get('email'));
  
  // Simulate download
  alert('Thank you! Your download will begin shortly. Check your email for additional resources.');
  
  closeDownloadModal();
  
  // Reset form
  this.reset();
});

// Close modal when clicking outside
document.getElementById('downloadModal').addEventListener('click', function(e) {
  if (e.target === this) {
    closeDownloadModal();
  }
});
</script>
