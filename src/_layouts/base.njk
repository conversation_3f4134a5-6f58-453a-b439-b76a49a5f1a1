<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ title }} | {{ site.title }}</title>
  <meta name="description" content="{{ description or site.description }}">

  <!-- SEO Meta Tags -->
  <meta name="keywords" content="{{ tags | join(', ') if tags }}{{ ', ' if tags }}IT services, cloud solutions, cybersecurity, digital transformation, small business technology">
  <meta name="author" content="{{ site.title }}">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="{{ site.url }}{{ page.url }}">

  <!-- Open Graph -->
  <meta property="og:title" content="{{ title }} | {{ site.title }}">
  <meta property="og:description" content="{{ description or site.description }}">
  <meta property="og:type" content="{% if page.url == '/' %}website{% else %}article{% endif %}">
  <meta property="og:url" content="{{ site.url }}{{ page.url }}">
  <meta property="og:site_name" content="{{ site.title }}">
  <meta property="og:image" content="{{ site.url }}{{ image or '/images/og-default.jpg' }}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{{ title }} | {{ site.title }}">
  <meta name="twitter:description" content="{{ description or site.description }}">
  <meta name="twitter:image" content="{{ site.url }}{{ image or '/images/og-default.jpg' }}">
  <meta name="twitter:site" content="@nextechsolutions">
  <meta name="twitter:creator" content="@nextechsolutions">

  <!-- Article specific meta tags -->
  {% if date %}
  <meta property="article:published_time" content="{{ date | dateToRfc3339 }}">
  <meta property="article:author" content="{{ author or 'NexTech Solutions' }}">
  {% if tags %}
  {% for tag in tags %}
  <meta property="article:tag" content="{{ tag }}">
  {% endfor %}
  {% endif %}
  {% endif %}

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "{% if page.url == '/' %}Organization{% else %}WebPage{% endif %}",
    {% if page.url == '/' %}
    "name": "{{ site.title }}",
    "description": "{{ site.description }}",
    "url": "{{ site.url }}",
    "logo": "{{ site.url }}/images/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-123-4567",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Business Ave",
      "addressLocality": "Tech City",
      "addressRegion": "TC",
      "postalCode": "12345",
      "addressCountry": "US"
    },
    "sameAs": [
      "https://linkedin.com/company/nextech-solutions",
      "https://twitter.com/nextechsolutions"
    ]
    {% else %}
    "name": "{{ title }}",
    "description": "{{ description or site.description }}",
    "url": "{{ site.url }}{{ page.url }}",
    "isPartOf": {
      "@type": "WebSite",
      "name": "{{ site.title }}",
      "url": "{{ site.url }}"
    }
    {% endif %}
  }
  </script>

  <!-- Additional structured data for blog posts -->
  {% if layout == 'blog-post.njk' %}
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": "{{ title }}",
    "description": "{{ excerpt or description }}",
    "image": "{{ site.url }}{{ image }}",
    "author": {
      "@type": "Person",
      "name": "{{ author or 'NexTech Solutions' }}"
    },
    "publisher": {
      "@type": "Organization",
      "name": "{{ site.title }}",
      "logo": {
        "@type": "ImageObject",
        "url": "{{ site.url }}/images/logo.png"
      }
    },
    "datePublished": "{{ date | dateToRfc3339 }}",
    "dateModified": "{{ date | dateToRfc3339 }}",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": "{{ site.url }}{{ page.url }}"
    }
  }
  </script>
  {% endif %}

  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://images.pexels.com">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Theme Color -->
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
</head>
<body class="min-h-screen bg-white">
  {% include "header.njk" %}
  
  <main class="flex-1">
    {{ content | safe }}
  </main>
  
  {% include "footer.njk" %}
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  {% if page.url == "/contact/" %}
    <script src="/js/contact-form.js"></script>
  {% endif %}
  {% if page.url == "/blog/" %}
    <script src="/js/search.js"></script>
  {% endif %}
</body>
</html> 