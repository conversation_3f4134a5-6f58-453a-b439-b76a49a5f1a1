---
layout: base.njk
title: Page Not Found
description: The page you're looking for doesn't exist. Find what you need with our site navigation or contact us for assistance.
permalink: /404.html
eleventyExcludeFromCollections: true
---

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 text-center">
    <div>
      <div class="mx-auto h-32 w-32 text-primary-600 mb-8">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Page Not Found</h2>
      <p class="text-lg text-gray-600 mb-8">
        Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
      </p>
    </div>

    <div class="space-y-4">
      <a
        href="/"
        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
      >
        Go to Homepage
      </a>
      
      <div class="grid grid-cols-2 gap-4">
        <a
          href="/services/"
          class="flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          Our Services
        </a>
        <a
          href="/contact/"
          class="flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          Contact Us
        </a>
      </div>
    </div>

    <div class="mt-8">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Pages</h3>
      <div class="space-y-2">
        <a href="/services/cloud-solutions/" class="block text-primary-600 hover:text-primary-800 transition-colors">Cloud Solutions</a>
        <a href="/services/cybersecurity/" class="block text-primary-600 hover:text-primary-800 transition-colors">Cybersecurity Services</a>
        <a href="/industries/healthcare/" class="block text-primary-600 hover:text-primary-800 transition-colors">Healthcare IT</a>
        <a href="/blog/" class="block text-primary-600 hover:text-primary-800 transition-colors">Technology Blog</a>
        <a href="/portfolio/" class="block text-primary-600 hover:text-primary-800 transition-colors">Case Studies</a>
      </div>
    </div>

    <div class="mt-8 pt-8 border-t border-gray-200">
      <p class="text-sm text-gray-500">
        Need help finding something specific? 
        <a href="/contact/" class="text-primary-600 hover:text-primary-800 font-medium">Contact our team</a> 
        and we'll be happy to assist you.
      </p>
    </div>
  </div>
</div>

<script>
  // Track 404 errors for analytics
  if (typeof gtag !== 'undefined') {
    gtag('event', 'page_view', {
      page_title: '404 - Page Not Found',
      page_location: window.location.href,
      custom_map: {'custom_parameter': 'error_404'}
    });
  }

  // Log the 404 for debugging
  console.warn('404 Error: Page not found -', window.location.href);
</script>
