---
layout: base.njk
title: Industries We Serve
description: Specialized IT solutions for healthcare, financial services, manufacturing, and other industries. Expert technology services tailored to your sector's unique needs.
permalink: /industries/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Industries We Serve
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Specialized IT solutions tailored to the unique challenges and requirements 
          of different industries. Deep expertise in sector-specific technology needs.
        </p>
      </div>
    </div>
  </section>

  <!-- Industry Overview -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Sector-Specific Expertise
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Every industry has unique technology challenges, compliance requirements, and operational needs. 
          Our specialized approach ensures solutions that fit your sector perfectly.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {% set industryStats = [
          {
            stat: "15+",
            description: "Industries Served"
          },
          {
            stat: "500+",
            description: "Sector-Specific Projects"
          },
          {
            stat: "98%",
            description: "Compliance Success Rate"
          },
          {
            stat: "24/7",
            description: "Industry-Aware Support"
          }
        ] %}

        {% for stat in industryStats %}
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">{{ stat.stat }}</div>
            <p class="text-gray-700 font-medium">{{ stat.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Primary Industries -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Primary Industry Focus
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Deep expertise and specialized solutions for industries with complex technology and compliance requirements.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {% set primaryIndustries = [
          {
            name: "Healthcare",
            description: "HIPAA-compliant IT solutions for medical practices, hospitals, and healthcare organizations.",
            image: "https://images.pexels.com/photos/263402/pexels-photo-263402.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            link: "/industries/healthcare/",
            features: ["Electronic Health Records", "HIPAA Compliance", "Telemedicine", "Medical Device Integration"],
            caseStudy: "/portfolio/healthcare-management-system/"
          },
          {
            name: "Financial Services",
            description: "Secure, compliant solutions for banks, credit unions, and financial institutions.",
            image: "https://images.pexels.com/photos/259027/pexels-photo-259027.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            link: "/industries/financial-services/",
            features: ["SOC 2 Compliance", "Digital Banking", "Cybersecurity", "Risk Management"],
            caseStudy: "/portfolio/financial-security-overhaul/"
          },
          {
            name: "Manufacturing",
            description: "Industry 4.0 solutions for modern manufacturing operations and digital transformation.",
            image: "https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
            link: "/industries/manufacturing/",
            features: ["IoT Integration", "Predictive Maintenance", "Quality Control", "Supply Chain"],
            caseStudy: "/portfolio/manufacturing-digital-transformation/"
          }
        ] %}

        {% for industry in primaryIndustries %}
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="{{ industry.image }}"
                alt="{{ industry.name }}"
                class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute bottom-4 left-4">
                <h3 class="text-2xl font-bold text-white">{{ industry.name }}</h3>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 mb-4 leading-relaxed">{{ industry.description }}</p>
              <ul class="space-y-2 mb-6">
                {% for feature in industry.features %}
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    {{ feature }}
                  </li>
                {% endfor %}
              </ul>
              <div class="flex flex-col space-y-3">
                <a
                  href="{{ industry.link }}"
                  class="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center"
                >
                  Learn More
                </a>
                <a
                  href="{{ industry.caseStudy }}"
                  class="border border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors text-center"
                >
                  View Case Study
                </a>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Additional Industries -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Additional Industries We Serve
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive IT solutions for a wide range of industries and business sectors.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {% set additionalIndustries = [
          {
            name: "Education",
            description: "Learning management systems and educational technology solutions",
            icon: "book",
            solutions: ["LMS Implementation", "Student Information Systems", "Virtual Classrooms", "Campus Networks"]
          },
          {
            name: "Legal Services",
            description: "Document management and case management systems for law firms",
            icon: "scale",
            solutions: ["Document Management", "Case Management", "Time Tracking", "Client Portals"]
          },
          {
            name: "Real Estate",
            description: "Property management and CRM solutions for real estate professionals",
            icon: "home",
            solutions: ["Property Management", "CRM Systems", "Virtual Tours", "Transaction Management"]
          },
          {
            name: "Non-Profit",
            description: "Cost-effective technology solutions for non-profit organizations",
            icon: "heart",
            solutions: ["Donor Management", "Volunteer Coordination", "Grant Tracking", "Event Management"]
          },
          {
            name: "Retail",
            description: "E-commerce and point-of-sale solutions for retail businesses",
            icon: "shopping",
            solutions: ["E-commerce Platforms", "POS Systems", "Inventory Management", "Customer Analytics"]
          },
          {
            name: "Professional Services",
            description: "Practice management and client relationship solutions",
            icon: "briefcase",
            solutions: ["Practice Management", "Client Portals", "Project Management", "Billing Systems"]
          },
          {
            name: "Hospitality",
            description: "Hotel and restaurant management technology solutions",
            icon: "building",
            solutions: ["Property Management", "Reservation Systems", "POS Solutions", "Guest Services"]
          },
          {
            name: "Transportation",
            description: "Fleet management and logistics technology solutions",
            icon: "truck",
            solutions: ["Fleet Management", "Route Optimization", "Tracking Systems", "Maintenance Scheduling"]
          }
        ] %}

        {% for industry in additionalIndustries %}
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              {% if industry.icon == 'book' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              {% elif industry.icon == 'scale' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"></path>
                </svg>
              {% elif industry.icon == 'home' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
              {% elif industry.icon == 'heart' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              {% elif industry.icon == 'shopping' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"></path>
                </svg>
              {% elif industry.icon == 'briefcase' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8"></path>
                </svg>
              {% elif industry.icon == 'building' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              {% elif industry.icon == 'truck' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2m0 0l2-2m-2 2v-6m6 6h4a2 2 0 002-2V8a2 2 0 00-2-2h-4m-6 8H5a2 2 0 01-2-2V8a2 2 0 012-2h4m6 8v2a2 2 0 01-2 2H7a2 2 0 01-2-2v-2"></path>
                </svg>
              {% endif %}
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ industry.name }}</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">{{ industry.description }}</p>
            <ul class="space-y-1">
              {% for solution in industry.solutions %}
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ solution }}
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Industry Expertise -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Why Choose Industry-Specific Expertise?
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Generic IT solutions often fall short of industry-specific requirements. Our sector expertise ensures optimal results.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% set expertisePoints = [
          {
            title: "Regulatory Compliance",
            description: "Deep understanding of industry regulations and compliance requirements to ensure your solutions meet all standards.",
            icon: "shield"
          },
          {
            title: "Best Practices",
            description: "Implementation of industry-proven best practices and methodologies for optimal performance and efficiency.",
            icon: "star"
          },
          {
            title: "Specialized Integrations",
            description: "Seamless integration with industry-specific software, systems, and third-party applications.",
            icon: "link"
          },
          {
            title: "Risk Management",
            description: "Industry-aware risk assessment and mitigation strategies to protect your business and data.",
            icon: "lock"
          },
          {
            title: "Faster Implementation",
            description: "Accelerated project timelines through pre-built industry templates and proven methodologies.",
            icon: "clock"
          },
          {
            title: "Ongoing Support",
            description: "Industry-knowledgeable support team that understands your unique operational requirements.",
            icon: "support"
          }
        ] %}

        {% for point in expertisePoints %}
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              {% if point.icon == 'shield' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              {% elif point.icon == 'star' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              {% elif point.icon == 'link' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              {% elif point.icon == 'lock' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              {% elif point.icon == 'clock' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              {% elif point.icon == 'support' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              {% endif %}
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ point.title }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ point.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Discuss Your Industry Needs?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Get a free consultation to discuss how our industry-specific expertise can benefit your organization.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Schedule Industry Consultation
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>
