---
layout: base.njk
title: Cybersecurity Services
description: Protect your business with enterprise-grade cybersecurity solutions. Comprehensive threat protection, compliance management, and 24/7 security monitoring.
permalink: /services/cybersecurity/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="animate-slide-up">
          <div class="flex items-center mb-6">
            <div class="w-16 h-16 bg-accent-600 rounded-xl flex items-center justify-center text-white mr-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold">
              Cybersecurity
            </h1>
          </div>
          <p class="text-xl text-blue-100 mb-8 leading-relaxed">
            Protect your business with enterprise-grade cybersecurity solutions. From threat detection to 
            incident response, we provide complete protection for your digital assets.
          </p>
          <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <a
              href="/contact/"
              class="bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-accent-700 transition-all duration-300 flex items-center justify-center group"
            >
              Get Security Assessment
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a
              href="/portfolio/"
              class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-all duration-300 text-center"
            >
              View Success Stories
            </a>
          </div>
        </div>
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <img
            src="https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
            alt="Cybersecurity Services"
            class="rounded-2xl shadow-2xl"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Threat Landscape -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          The Current Threat Landscape
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Cyber threats are evolving rapidly. Small and medium businesses are increasingly targeted by sophisticated attacks.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {% set threats = [
          {
            stat: "43%",
            description: "of cyberattacks target small businesses"
          },
          {
            stat: "$4.45M",
            description: "average cost of a data breach in 2024"
          },
          {
            stat: "60%",
            description: "of small businesses close within 6 months of a cyberattack"
          },
          {
            stat: "300%",
            description: "increase in ransomware attacks since 2020"
          }
        ] %}

        {% for threat in threats %}
          <div class="text-center p-6 bg-red-50 rounded-xl animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="text-3xl font-bold text-red-600 mb-2">{{ threat.stat }}</div>
            <p class="text-gray-700 font-medium">{{ threat.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Our Security Services -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Comprehensive Security Solutions
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Multi-layered security approach to protect your business from all types of cyber threats.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {% set securityServices = [
          {
            title: "Threat Assessment & Monitoring",
            description: "Continuous monitoring and assessment of your security posture with real-time threat detection.",
            features: ["24/7 Security Monitoring", "Vulnerability Assessments", "Penetration Testing", "Threat Intelligence"]
          },
          {
            title: "Endpoint Protection",
            description: "Advanced protection for all devices accessing your network, including laptops, desktops, and mobile devices.",
            features: ["Anti-malware Protection", "Behavioral Analysis", "Device Control", "Remote Wipe Capabilities"]
          },
          {
            title: "Network Security",
            description: "Comprehensive network protection with firewalls, intrusion detection, and secure access controls.",
            features: ["Next-Gen Firewalls", "Intrusion Prevention", "VPN Solutions", "Network Segmentation"]
          },
          {
            title: "Email Security",
            description: "Advanced email protection against phishing, malware, and business email compromise attacks.",
            features: ["Anti-phishing Protection", "Email Encryption", "Spam Filtering", "Attachment Scanning"]
          },
          {
            title: "Data Protection",
            description: "Comprehensive data protection with encryption, backup, and access controls to prevent data loss.",
            features: ["Data Encryption", "Access Controls", "Data Loss Prevention", "Secure Backup Solutions"]
          },
          {
            title: "Compliance Management",
            description: "Ensure compliance with industry regulations and standards through comprehensive security frameworks.",
            features: ["HIPAA Compliance", "PCI DSS", "SOC 2", "GDPR Compliance"]
          }
        ] %}

        {% for service in securityServices %}
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ service.title }}</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">{{ service.description }}</p>
            <ul class="space-y-2">
              {% for feature in service.features %}
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ feature }}
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Security Framework -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Security Framework
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Based on industry best practices and proven methodologies for comprehensive protection.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {% set frameworks = [
          {
            title: "NIST Cybersecurity Framework",
            description: "Comprehensive framework covering Identify, Protect, Detect, Respond, and Recover functions.",
            components: ["Risk Assessment", "Asset Management", "Access Control", "Incident Response"]
          },
          {
            title: "Zero Trust Architecture",
            description: "Never trust, always verify approach with continuous authentication and authorization.",
            components: ["Identity Verification", "Device Trust", "Network Segmentation", "Data Protection"]
          },
          {
            title: "Defense in Depth",
            description: "Multi-layered security approach with redundant protective measures at every level.",
            components: ["Perimeter Security", "Network Security", "Endpoint Protection", "Data Security"]
          }
        ] %}

        {% for framework in frameworks %}
          <div class="bg-white p-8 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ framework.title }}</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">{{ framework.description }}</p>
            <ul class="space-y-2">
              {% for component in framework.components %}
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-primary-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  {{ component }}
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Incident Response -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          24/7 Incident Response
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Rapid response to security incidents with expert analysis and remediation.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        {% set responseSteps = [
          {
            step: "01",
            title: "Detection",
            description: "Immediate identification and analysis of security incidents through advanced monitoring.",
            time: "< 5 minutes"
          },
          {
            step: "02",
            title: "Containment",
            description: "Rapid containment of threats to prevent spread and minimize damage to your systems.",
            time: "< 15 minutes"
          },
          {
            step: "03",
            title: "Investigation",
            description: "Thorough forensic analysis to understand the scope and impact of the incident.",
            time: "< 1 hour"
          },
          {
            step: "04",
            title: "Recovery",
            description: "Complete system restoration and implementation of measures to prevent recurrence.",
            time: "< 4 hours"
          }
        ] %}

        {% for step in responseSteps %}
          <div class="text-center animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-16 h-16 bg-red-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              {{ step.step }}
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ step.title }}</h3>
            <div class="text-sm font-semibold text-red-600 mb-3">{{ step.time }}</div>
            <p class="text-gray-600 leading-relaxed">{{ step.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Compliance -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Compliance & Certifications
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Meet regulatory requirements and industry standards with our comprehensive compliance solutions.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {% set compliance = [
          {
            name: "HIPAA",
            description: "Healthcare data protection and privacy compliance",
            icon: "medical"
          },
          {
            name: "PCI DSS",
            description: "Payment card industry data security standards",
            icon: "credit-card"
          },
          {
            name: "SOC 2",
            description: "Service organization control for security and availability",
            icon: "shield"
          },
          {
            name: "GDPR",
            description: "General data protection regulation compliance",
            icon: "globe"
          }
        ] %}

        {% for standard in compliance %}
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 text-center animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-16 h-16 bg-primary-100 rounded-xl flex items-center justify-center text-primary-600 mx-auto mb-4">
              {% if standard.icon == 'medical' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              {% elif standard.icon == 'credit-card' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
              {% elif standard.icon == 'shield' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              {% elif standard.icon == 'globe' %}
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                </svg>
              {% endif %}
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-2">{{ standard.name }}</h3>
            <p class="text-gray-600 text-sm leading-relaxed">{{ standard.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Pricing -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Security Investment Plans
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive security solutions designed to fit your business size and security requirements.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {% set securityPlans = [
          {
            name: "Essential Security",
            price: "Starting at $1,500/month",
            description: "Basic security protection for small businesses with essential security controls.",
            features: ["Endpoint Protection", "Email Security", "Basic Monitoring", "Monthly Reports", "Business Hours Support"]
          },
          {
            name: "Advanced Security",
            price: "Starting at $3,500/month",
            description: "Comprehensive security solution with advanced threat detection and response.",
            features: ["24/7 Monitoring", "Advanced Threat Detection", "Incident Response", "Compliance Support", "Security Training"],
            popular: true
          },
          {
            name: "Enterprise Security",
            price: "Custom Pricing",
            description: "Full-scale security program with dedicated security team and custom solutions.",
            features: ["Dedicated Security Team", "Custom Security Architecture", "Advanced Compliance", "Executive Reporting", "Strategic Security Planning"]
          }
        ] %}

        {% for plan in securityPlans %}
          <div class="bg-white p-8 rounded-xl border-2 {{ 'border-primary-500 shadow-xl' if plan.popular else 'border-gray-200' }} hover:shadow-lg transition-all duration-300 relative animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            {% if plan.popular %}
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span class="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
            {% endif %}
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ plan.name }}</h3>
              <div class="text-3xl font-bold text-primary-600 mb-2">{{ plan.price }}</div>
              <p class="text-gray-600">{{ plan.description }}</p>
            </div>
            <ul class="space-y-3 mb-8">
              {% for feature in plan.features %}
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ feature }}
                </li>
              {% endfor %}
            </ul>
            <a
              href="/contact/"
              class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center block"
            >
              Get Started
            </a>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Don't Wait for a Security Incident
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Protect your business today with a comprehensive security assessment and customized protection plan.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Get Free Security Assessment
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>
