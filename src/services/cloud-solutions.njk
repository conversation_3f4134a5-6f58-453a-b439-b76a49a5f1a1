---
layout: base.njk
title: Cloud Solutions
description: Transform your business with our comprehensive cloud solutions. Migrate to the cloud, optimize costs, and scale your infrastructure with expert guidance.
permalink: /services/cloud-solutions/
---

<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="animate-slide-up">
          <div class="flex items-center mb-6">
            <div class="w-16 h-16 bg-accent-600 rounded-xl flex items-center justify-center text-white mr-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
              </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold">
              Cloud Solutions
            </h1>
          </div>
          <p class="text-xl text-blue-100 mb-8 leading-relaxed">
            Transform your business with our comprehensive cloud solutions. We help you migrate to the cloud, 
            optimize costs, and scale your infrastructure to meet growing demands.
          </p>
          <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <a
              href="/contact/"
              class="bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-accent-700 transition-all duration-300 flex items-center justify-center group"
            >
              Get Free Assessment
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a
              href="/portfolio/"
              class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-all duration-300 text-center"
            >
              View Case Studies
            </a>
          </div>
        </div>
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <img
            src="https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
            alt="Cloud Solutions"
            class="rounded-2xl shadow-2xl"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Key Benefits -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Why Choose Cloud Solutions?
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Cloud computing offers unprecedented flexibility, scalability, and cost-effectiveness for modern businesses.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% set benefits = [
          {
            icon: "dollar",
            title: "Cost Reduction",
            description: "Reduce infrastructure costs by up to 35% with pay-as-you-use cloud services and eliminate expensive hardware investments."
          },
          {
            icon: "scale",
            title: "Scalability",
            description: "Scale resources up or down instantly based on demand, ensuring optimal performance during peak periods."
          },
          {
            icon: "shield",
            title: "Enhanced Security",
            description: "Benefit from enterprise-grade security measures, compliance certifications, and 24/7 monitoring."
          },
          {
            icon: "globe",
            title: "Global Accessibility",
            description: "Access your systems and data from anywhere in the world, enabling remote work and global collaboration."
          },
          {
            icon: "backup",
            title: "Disaster Recovery",
            description: "Built-in backup and recovery capabilities ensure business continuity and data protection."
          },
          {
            icon: "update",
            title: "Automatic Updates",
            description: "Stay current with the latest features and security patches without manual intervention."
          }
        ] %}

        {% for benefit in benefits %}
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              {% if benefit.icon == 'dollar' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              {% elif benefit.icon == 'scale' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                </svg>
              {% elif benefit.icon == 'shield' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              {% elif benefit.icon == 'globe' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                </svg>
              {% elif benefit.icon == 'backup' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                </svg>
              {% elif benefit.icon == 'update' %}
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              {% endif %}
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ benefit.title }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ benefit.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Our Services -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Cloud Services
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive cloud solutions tailored to your business needs and objectives.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {% set cloudServices = [
          {
            title: "Cloud Migration",
            description: "Seamless migration of your existing infrastructure to the cloud with minimal downtime and maximum efficiency.",
            features: ["Assessment & Planning", "Data Migration", "Application Modernization", "Testing & Validation"]
          },
          {
            title: "Infrastructure Management",
            description: "Complete management of your cloud infrastructure with 24/7 monitoring, optimization, and support.",
            features: ["24/7 Monitoring", "Performance Optimization", "Security Management", "Backup & Recovery"]
          },
          {
            title: "Cost Optimization",
            description: "Continuous optimization of your cloud spending through right-sizing, reserved instances, and automation.",
            features: ["Cost Analysis", "Resource Right-sizing", "Reserved Instance Planning", "Automated Scaling"]
          },
          {
            title: "Multi-Cloud Strategy",
            description: "Design and implement multi-cloud architectures for improved resilience and vendor independence.",
            features: ["Multi-Cloud Architecture", "Vendor Management", "Data Synchronization", "Unified Monitoring"]
          }
        ] %}

        {% for service in cloudServices %}
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ service.title }}</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">{{ service.description }}</p>
            <ul class="space-y-2">
              {% for feature in service.features %}
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ feature }}
                </li>
              {% endfor %}
            </ul>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Cloud Platforms -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Cloud Platforms We Support
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          We're certified experts in all major cloud platforms, ensuring you get the best solution for your needs.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {% set platforms = [
          {
            name: "Amazon Web Services",
            logo: "AWS",
            description: "The world's most comprehensive cloud platform with over 200 services.",
            certifications: ["AWS Solutions Architect", "AWS DevOps Engineer", "AWS Security Specialist"]
          },
          {
            name: "Microsoft Azure",
            logo: "Azure",
            description: "Integrated cloud services for analytics, computing, database, mobile, and web.",
            certifications: ["Azure Solutions Architect", "Azure Administrator", "Azure Security Engineer"]
          },
          {
            name: "Google Cloud Platform",
            logo: "GCP",
            description: "Google's suite of cloud computing services with advanced AI and ML capabilities.",
            certifications: ["GCP Professional Architect", "GCP Data Engineer", "GCP Security Engineer"]
          }
        ] %}

        {% for platform in platforms %}
          <div class="bg-white p-8 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 text-center animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-20 h-20 bg-primary-100 rounded-xl flex items-center justify-center text-primary-600 mx-auto mb-6">
              <span class="text-lg font-bold">{{ platform.logo }}</span>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">{{ platform.name }}</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">{{ platform.description }}</p>
            <div class="space-y-2">
              {% for cert in platform.certifications %}
                <div class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  {{ cert }}
                </div>
              {% endfor %}
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Process -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Cloud Migration Process
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          A proven methodology that ensures successful cloud adoption with minimal risk and maximum value.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {% set processSteps = [
          {
            step: "01",
            title: "Assessment",
            description: "Comprehensive analysis of your current infrastructure, applications, and business requirements."
          },
          {
            step: "02",
            title: "Strategy",
            description: "Development of a customized cloud strategy aligned with your business goals and budget."
          },
          {
            step: "03",
            title: "Migration",
            description: "Phased migration execution with minimal downtime and comprehensive testing."
          },
          {
            step: "04",
            title: "Optimization",
            description: "Ongoing optimization, monitoring, and support to maximize your cloud investment."
          }
        ] %}

        {% for step in processSteps %}
          <div class="text-center animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              {{ step.step }}
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ step.title }}</h3>
            <p class="text-gray-600 leading-relaxed">{{ step.description }}</p>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- Pricing -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Investment Options
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Flexible pricing options designed to fit your budget and business needs.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        {% set pricingTiers = [
          {
            name: "Assessment",
            price: "Starting at $2,500",
            description: "Comprehensive cloud readiness assessment and migration planning.",
            features: ["Infrastructure Analysis", "Cost Estimation", "Migration Roadmap", "Risk Assessment", "ROI Calculation"]
          },
          {
            name: "Migration",
            price: "Starting at $15,000",
            description: "Complete cloud migration with ongoing support and optimization.",
            features: ["Full Migration Service", "24/7 Support During Migration", "Testing & Validation", "Staff Training", "3 Months Post-Migration Support"],
            popular: true
          },
          {
            name: "Managed Services",
            price: "Starting at $3,000/month",
            description: "Ongoing cloud management, monitoring, and optimization services.",
            features: ["24/7 Monitoring", "Performance Optimization", "Security Management", "Cost Optimization", "Monthly Reporting"]
          }
        ] %}

        {% for tier in pricingTiers %}
          <div class="bg-white p-8 rounded-xl border-2 {{ 'border-primary-500 shadow-xl' if tier.popular else 'border-gray-200' }} hover:shadow-lg transition-all duration-300 relative animate-scale-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            {% if tier.popular %}
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span class="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
            {% endif %}
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ tier.name }}</h3>
              <div class="text-3xl font-bold text-primary-600 mb-2">{{ tier.price }}</div>
              <p class="text-gray-600">{{ tier.description }}</p>
            </div>
            <ul class="space-y-3 mb-8">
              {% for feature in tier.features %}
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{ feature }}
                </li>
              {% endfor %}
            </ul>
            <a
              href="/contact/"
              class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center block"
            >
              Get Started
            </a>
          </div>
        {% endfor %}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Move to the Cloud?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Start your cloud journey today with a free assessment and consultation from our certified cloud experts.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Get Free Cloud Assessment
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>
