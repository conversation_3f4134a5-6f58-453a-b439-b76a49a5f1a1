<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Manufacturing IT Solutions | </title>
  <meta name="description" content="Industry 4.0 solutions for manufacturers. IoT, automation, predictive maintenance, and digital transformation for modern manufacturing operations.">

  <!-- SEO Meta Tags -->
  <meta name="keywords" content="IT services, cloud solutions, cybersecurity, digital transformation, small business technology">
  <meta name="author" content="">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="/industries/manufacturing/">

  <!-- Open Graph -->
  <meta property="og:title" content="Manufacturing IT Solutions | ">
  <meta property="og:description" content="Industry 4.0 solutions for manufacturers. IoT, automation, predictive maintenance, and digital transformation for modern manufacturing operations.">
  <meta property="og:type" content="article">
  <meta property="og:url" content="/industries/manufacturing/">
  <meta property="og:site_name" content="">
  <meta property="og:image" content="/images/og-default.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Manufacturing IT Solutions | ">
  <meta name="twitter:description" content="Industry 4.0 solutions for manufacturers. IoT, automation, predictive maintenance, and digital transformation for modern manufacturing operations.">
  <meta name="twitter:image" content="/images/og-default.jpg">
  <meta name="twitter:site" content="@nextechsolutions">
  <meta name="twitter:creator" content="@nextechsolutions">

  <!-- Article specific meta tags -->
  

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    
    "name": "Manufacturing IT Solutions",
    "description": "Industry 4.0 solutions for manufacturers. IoT, automation, predictive maintenance, and digital transformation for modern manufacturing operations.",
    "url": "/industries/manufacturing/",
    "isPartOf": {
      "@type": "WebSite",
      "name": "",
      "url": ""
    }
    
  }
  </script>

  <!-- Additional structured data for blog posts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://images.pexels.com">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Theme Color -->
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
</head>
<body class="min-h-screen bg-white">
  <header class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <a href="/" class="flex items-center space-x-2">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">N</span>
        </div>
        <span class="text-2xl font-bold text-gray-900"></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        
      </nav>

      <div class="hidden md:flex items-center space-x-4">
        <a
          href="/contact/"
          class="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Get Started
        </a>
      </div>

      <!-- Mobile menu button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden hidden py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        
        <a
          href="/contact/"
          class="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
        >
          Get Started
        </a>
      </div>
    </div>
  </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script> 
  
  <main class="flex-1">
    
<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="animate-slide-up">
          <div class="flex items-center mb-6">
            <div class="w-16 h-16 bg-accent-600 rounded-xl flex items-center justify-center text-white mr-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold">
              Manufacturing IT Solutions
            </h1>
          </div>
          <p class="text-xl text-blue-100 mb-8 leading-relaxed">
            Transform your manufacturing operations with Industry 4.0 technologies. 
            IoT, automation, and digital solutions for modern manufacturing excellence.
          </p>
          <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <a
              href="/contact/"
              class="bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-accent-700 transition-all duration-300 flex items-center justify-center group"
            >
              Get Digital Assessment
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a
              href="/portfolio/manufacturing-digital-transformation/"
              class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-all duration-300 text-center"
            >
              View Case Study
            </a>
          </div>
        </div>
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <img
            src="https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
            alt="Manufacturing IT Solutions"
            class="rounded-2xl shadow-2xl"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Manufacturing Challenges -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Modern Manufacturing Challenges
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Today's manufacturers face complex challenges that require innovative technology solutions and digital transformation.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Equipment Downtime</h3>
            <p class="text-gray-600 leading-relaxed">Unplanned equipment failures causing production delays and increased maintenance costs.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Quality Control</h3>
            <p class="text-gray-600 leading-relaxed">Maintaining consistent product quality while reducing inspection time and costs.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2m0 0l2-2m-2 2v-6m6 6h4a2 2 0 002-2V8a2 2 0 00-2-2h-4m-6 8H5a2 2 0 01-2-2V8a2 2 0 012-2h4m6 8v2a2 2 0 01-2 2H7a2 2 0 01-2-2v-2"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Supply Chain Visibility</h3>
            <p class="text-gray-600 leading-relaxed">Lack of real-time visibility into supply chain operations and inventory levels.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Data Silos</h3>
            <p class="text-gray-600 leading-relaxed">Disconnected systems preventing comprehensive analysis and decision-making.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Workforce Efficiency</h3>
            <p class="text-gray-600 leading-relaxed">Optimizing workforce productivity and reducing manual, repetitive tasks.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-red-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Regulatory Compliance</h3>
            <p class="text-gray-600 leading-relaxed">Meeting industry standards and regulatory requirements for safety and quality.</p>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Industry 4.0 Solutions -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Industry 4.0 Solutions
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive digital transformation solutions that modernize manufacturing operations and drive efficiency.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        

        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">IoT and Smart Sensors</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Connect machines and equipment for real-time monitoring and data collection.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Equipment Monitoring
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Environmental Sensors
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Predictive Analytics
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Real-time Alerts
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Data Visualization
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Predictive Maintenance</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">AI-powered maintenance scheduling to prevent equipment failures and reduce downtime.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Failure Prediction
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Maintenance Scheduling
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Parts Optimization
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Cost Reduction
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Performance Analytics
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Manufacturing Execution Systems</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Comprehensive MES solutions for production planning, scheduling, and quality management.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Production Planning
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Work Order Management
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Control
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Inventory Tracking
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Performance Reporting
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Digital Twin Technology</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Virtual replicas of physical assets for simulation, optimization, and predictive analysis.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Virtual Modeling
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Process Simulation
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Performance Optimization
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Scenario Testing
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Predictive Analysis
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Automated Quality Control</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">AI-powered quality inspection systems for consistent product quality and reduced defects.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Vision Inspection
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Automated Testing
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Defect Detection
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Analytics
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Compliance Reporting
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Supply Chain Integration</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">End-to-end supply chain visibility and optimization for improved efficiency.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Supplier Integration
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Inventory Optimization
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Demand Forecasting
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Logistics Tracking
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Performance Metrics
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Manufacturing Types -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Manufacturing Industries We Serve
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Specialized solutions for different manufacturing sectors and production environments.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Automotive Manufacturing</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Precision manufacturing for automotive components and assembly</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Control
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Supply Chain
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Predictive Maintenance
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Compliance Tracking
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Aerospace &amp; Defense</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">High-precision manufacturing with strict quality and compliance requirements</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Assurance
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Traceability
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Compliance Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Precision Monitoring
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Electronics Manufacturing</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">High-volume electronics production and assembly operations</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Automated Testing
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Inventory Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Process Control
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Yield Optimization
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Food &amp; Beverage</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Food processing and packaging with safety and quality focus</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Safety Monitoring
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Batch Tracking
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Control
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Regulatory Compliance
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Pharmaceuticals</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Regulated pharmaceutical manufacturing and packaging</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  GMP Compliance
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Batch Records
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Systems
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Validation Management
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Textiles &amp; Apparel</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Textile production and garment manufacturing operations</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Production Planning
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Inventory Control
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Order Tracking
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Technology Stack -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Manufacturing Technology Stack
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Advanced technologies and platforms that power modern manufacturing operations.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        

        
          <div class="text-center animate-scale-in" style="animation-delay: 0s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">IoT Platforms</h3>
            <ul class="space-y-2">
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Siemens MindSphere
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  GE Predix
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Microsoft Azure IoT
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  AWS IoT Core
                </li>
              
            </ul>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.1s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Manufacturing Systems</h3>
            <ul class="space-y-2">
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  SAP Manufacturing
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Rockwell FactoryTalk
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Wonderware
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Ignition SCADA
                </li>
              
            </ul>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.2s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Analytics &amp; AI</h3>
            <ul class="space-y-2">
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Machine Learning
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Predictive Analytics
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Computer Vision
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Digital Twins
                </li>
              
            </ul>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Integration</h3>
            <ul class="space-y-2">
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  OPC UA
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  MQTT
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  REST APIs
                </li>
              
                <li class="bg-white px-3 py-2 rounded-lg text-sm text-gray-700 border border-gray-200">
                  Enterprise Service Bus
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Success Metrics -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Manufacturing Success Metrics
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Proven results our manufacturing clients have achieved through digital transformation.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        

        
          <div class="text-center animate-scale-in" style="animation-delay: 0s;">
            <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">40%</div>
            <div class="text-gray-600 font-medium">Increase in OEE</div>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.1s;">
            <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">60%</div>
            <div class="text-gray-600 font-medium">Reduction in Downtime</div>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.2s;">
            <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">80%</div>
            <div class="text-gray-600 font-medium">Decrease in Defect Rates</div>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">25%</div>
            <div class="text-gray-600 font-medium">Improvement in Throughput</div>
          </div>
        
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready for Industry 4.0 Transformation?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Start your manufacturing digital transformation with a comprehensive assessment of your current operations.
        </p>
        <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
          <a
            href="/contact/"
            class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
          >
            Get Digital Assessment
            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a
            href="/portfolio/manufacturing-digital-transformation/"
            class="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-all duration-300"
          >
            View Manufacturing Case Study
          </a>
        </div>
      </div>
    </div>
  </section>
</div>

  </main>
  
  <footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">N</span>
          </div>
          <span class="text-2xl font-bold"></span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md">
          Empowering SMEs with cutting-edge IT solutions. We transform businesses through 
          technology, delivering innovative services that drive growth and efficiency.
        </p>
        <div class="flex space-x-4">
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span></span>
          </div>
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span></span>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Services</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/services/cloud-solutions" class="hover:text-white transition-colors">Cloud Solutions</a></li>
          <li><a href="/services/cybersecurity" class="hover:text-white transition-colors">Cybersecurity</a></li>
          <li><a href="/services/digital-transformation" class="hover:text-white transition-colors">Digital Transformation</a></li>
          <li><a href="/services/it-consulting" class="hover:text-white transition-colors">IT Consulting</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Company</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
          <li><a href="/portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
          <li><a href="/blog" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-300 text-sm">
        © 2025 . All rights reserved.
      </p>
      <div class="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-300">
        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
      </div>
    </div>
  </div>
</footer>

<!-- Scroll to Top Button -->
<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 hidden"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const scrollToTopButton = document.getElementById('scroll-to-top');
  
  if (scrollToTopButton) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 400) {
        scrollToTopButton.classList.remove('hidden');
      } else {
        scrollToTopButton.classList.add('hidden');
      }
    });
    
    scrollToTopButton.addEventListener('click', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});
</script> 
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  
  
</body>
</html> 