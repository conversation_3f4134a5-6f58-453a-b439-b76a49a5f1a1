<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Industries We Serve | </title>
  <meta name="description" content="Specialized IT solutions for healthcare, financial services, manufacturing, and other industries. Expert technology services tailored to your sector&#39;s unique needs.">

  <!-- SEO Meta Tags -->
  <meta name="keywords" content="IT services, cloud solutions, cybersecurity, digital transformation, small business technology">
  <meta name="author" content="">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="/industries/">

  <!-- Open Graph -->
  <meta property="og:title" content="Industries We Serve | ">
  <meta property="og:description" content="Specialized IT solutions for healthcare, financial services, manufacturing, and other industries. Expert technology services tailored to your sector&#39;s unique needs.">
  <meta property="og:type" content="article">
  <meta property="og:url" content="/industries/">
  <meta property="og:site_name" content="">
  <meta property="og:image" content="/images/og-default.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Industries We Serve | ">
  <meta name="twitter:description" content="Specialized IT solutions for healthcare, financial services, manufacturing, and other industries. Expert technology services tailored to your sector&#39;s unique needs.">
  <meta name="twitter:image" content="/images/og-default.jpg">
  <meta name="twitter:site" content="@nextechsolutions">
  <meta name="twitter:creator" content="@nextechsolutions">

  <!-- Article specific meta tags -->
  

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    
    "name": "Industries We Serve",
    "description": "Specialized IT solutions for healthcare, financial services, manufacturing, and other industries. Expert technology services tailored to your sector&#39;s unique needs.",
    "url": "/industries/",
    "isPartOf": {
      "@type": "WebSite",
      "name": "",
      "url": ""
    }
    
  }
  </script>

  <!-- Additional structured data for blog posts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://images.pexels.com">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Theme Color -->
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
</head>
<body class="min-h-screen bg-white">
  <header class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <a href="/" class="flex items-center space-x-2">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">N</span>
        </div>
        <span class="text-2xl font-bold text-gray-900"></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        
      </nav>

      <div class="hidden md:flex items-center space-x-4">
        <a
          href="/contact/"
          class="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Get Started
        </a>
      </div>

      <!-- Mobile menu button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden hidden py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        
        <a
          href="/contact/"
          class="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
        >
          Get Started
        </a>
      </div>
    </div>
  </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script> 
  
  <main class="flex-1">
    
<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Industries We Serve
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Specialized IT solutions tailored to the unique challenges and requirements 
          of different industries. Deep expertise in sector-specific technology needs.
        </p>
      </div>
    </div>
  </section>

  <!-- Industry Overview -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Sector-Specific Expertise
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Every industry has unique technology challenges, compliance requirements, and operational needs. 
          Our specialized approach ensures solutions that fit your sector perfectly.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        

        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">15+</div>
            <p class="text-gray-700 font-medium">Industries Served</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.1s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">500+</div>
            <p class="text-gray-700 font-medium">Sector-Specific Projects</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.2s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">98%</div>
            <p class="text-gray-700 font-medium">Compliance Success Rate</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">24/7</div>
            <p class="text-gray-700 font-medium">Industry-Aware Support</p>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Primary Industries -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Primary Industry Focus
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Deep expertise and specialized solutions for industries with complex technology and compliance requirements.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/263402/pexels-photo-263402.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Healthcare"
                class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute bottom-4 left-4">
                <h3 class="text-2xl font-bold text-white">Healthcare</h3>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 mb-4 leading-relaxed">HIPAA-compliant IT solutions for medical practices, hospitals, and healthcare organizations.</p>
              <ul class="space-y-2 mb-6">
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Electronic Health Records
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    HIPAA Compliance
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Telemedicine
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Medical Device Integration
                  </li>
                
              </ul>
              <div class="flex flex-col space-y-3">
                <a
                  href="/industries/healthcare/"
                  class="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center"
                >
                  Learn More
                </a>
                <a
                  href="/portfolio/healthcare-management-system/"
                  class="border border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors text-center"
                >
                  View Case Study
                </a>
              </div>
            </div>
          </div>
        
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/259027/pexels-photo-259027.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Financial Services"
                class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute bottom-4 left-4">
                <h3 class="text-2xl font-bold text-white">Financial Services</h3>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 mb-4 leading-relaxed">Secure, compliant solutions for banks, credit unions, and financial institutions.</p>
              <ul class="space-y-2 mb-6">
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    SOC 2 Compliance
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Digital Banking
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Cybersecurity
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Risk Management
                  </li>
                
              </ul>
              <div class="flex flex-col space-y-3">
                <a
                  href="/industries/financial-services/"
                  class="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center"
                >
                  Learn More
                </a>
                <a
                  href="/portfolio/financial-security-overhaul/"
                  class="border border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors text-center"
                >
                  View Case Study
                </a>
              </div>
            </div>
          </div>
        
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Manufacturing"
                class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div class="absolute bottom-4 left-4">
                <h3 class="text-2xl font-bold text-white">Manufacturing</h3>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 mb-4 leading-relaxed">Industry 4.0 solutions for modern manufacturing operations and digital transformation.</p>
              <ul class="space-y-2 mb-6">
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    IoT Integration
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Predictive Maintenance
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Quality Control
                  </li>
                
                  <li class="flex items-center text-gray-600">
                    <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Supply Chain
                  </li>
                
              </ul>
              <div class="flex flex-col space-y-3">
                <a
                  href="/industries/manufacturing/"
                  class="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center"
                >
                  Learn More
                </a>
                <a
                  href="/portfolio/manufacturing-digital-transformation/"
                  class="border border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors text-center"
                >
                  View Case Study
                </a>
              </div>
            </div>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Additional Industries -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Additional Industries We Serve
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive IT solutions for a wide range of industries and business sectors.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        

        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Education</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Learning management systems and educational technology solutions</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  LMS Implementation
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Student Information Systems
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Virtual Classrooms
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Campus Networks
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Legal Services</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Document management and case management systems for law firms</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Document Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Case Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Time Tracking
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Client Portals
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Real Estate</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Property management and CRM solutions for real estate professionals</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Property Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  CRM Systems
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Virtual Tours
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Transaction Management
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Non-Profit</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Cost-effective technology solutions for non-profit organizations</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Donor Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Volunteer Coordination
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Grant Tracking
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Event Management
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Retail</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">E-commerce and point-of-sale solutions for retail businesses</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  E-commerce Platforms
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  POS Systems
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Inventory Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Customer Analytics
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Professional Services</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Practice management and client relationship solutions</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Practice Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Client Portals
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Project Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Billing Systems
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.6000000000000001s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Hospitality</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Hotel and restaurant management technology solutions</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Property Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Reservation Systems
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  POS Solutions
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Guest Services
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.7000000000000001s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2m0 0l2-2m-2 2v-6m6 6h4a2 2 0 002-2V8a2 2 0 00-2-2h-4m-6 8H5a2 2 0 01-2-2V8a2 2 0 012-2h4m6 8v2a2 2 0 01-2 2H7a2 2 0 01-2-2v-2"></path>
                </svg>
              
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Transportation</h3>
            <p class="text-gray-600 text-sm mb-4 leading-relaxed">Fleet management and logistics technology solutions</p>
            <ul class="space-y-1">
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Fleet Management
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Route Optimization
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Tracking Systems
                </li>
              
                <li class="text-xs text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Maintenance Scheduling
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Industry Expertise -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Why Choose Industry-Specific Expertise?
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Generic IT solutions often fall short of industry-specific requirements. Our sector expertise ensures optimal results.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Regulatory Compliance</h3>
            <p class="text-gray-600 leading-relaxed">Deep understanding of industry regulations and compliance requirements to ensure your solutions meet all standards.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Best Practices</h3>
            <p class="text-gray-600 leading-relaxed">Implementation of industry-proven best practices and methodologies for optimal performance and efficiency.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Specialized Integrations</h3>
            <p class="text-gray-600 leading-relaxed">Seamless integration with industry-specific software, systems, and third-party applications.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Risk Management</h3>
            <p class="text-gray-600 leading-relaxed">Industry-aware risk assessment and mitigation strategies to protect your business and data.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Faster Implementation</h3>
            <p class="text-gray-600 leading-relaxed">Accelerated project timelines through pre-built industry templates and proven methodologies.</p>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mb-4">
              
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Ongoing Support</h3>
            <p class="text-gray-600 leading-relaxed">Industry-knowledgeable support team that understands your unique operational requirements.</p>
          </div>
        
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Discuss Your Industry Needs?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Get a free consultation to discuss how our industry-specific expertise can benefit your organization.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Schedule Industry Consultation
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>

  </main>
  
  <footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">N</span>
          </div>
          <span class="text-2xl font-bold"></span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md">
          Empowering SMEs with cutting-edge IT solutions. We transform businesses through 
          technology, delivering innovative services that drive growth and efficiency.
        </p>
        <div class="flex space-x-4">
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span></span>
          </div>
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span></span>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Services</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/services/cloud-solutions" class="hover:text-white transition-colors">Cloud Solutions</a></li>
          <li><a href="/services/cybersecurity" class="hover:text-white transition-colors">Cybersecurity</a></li>
          <li><a href="/services/digital-transformation" class="hover:text-white transition-colors">Digital Transformation</a></li>
          <li><a href="/services/it-consulting" class="hover:text-white transition-colors">IT Consulting</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Company</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
          <li><a href="/portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
          <li><a href="/blog" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-300 text-sm">
        © 2025 . All rights reserved.
      </p>
      <div class="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-300">
        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
      </div>
    </div>
  </div>
</footer>

<!-- Scroll to Top Button -->
<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 hidden"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const scrollToTopButton = document.getElementById('scroll-to-top');
  
  if (scrollToTopButton) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 400) {
        scrollToTopButton.classList.remove('hidden');
      } else {
        scrollToTopButton.classList.add('hidden');
      }
    });
    
    scrollToTopButton.addEventListener('click', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});
</script> 
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  
  
</body>
</html> 