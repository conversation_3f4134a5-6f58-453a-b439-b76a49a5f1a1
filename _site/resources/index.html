<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Free IT Resources | </title>
  <meta name="description" content="Download free IT guides, checklists, and whitepapers. Expert insights on cloud migration, cybersecurity, digital transformation, and technology best practices.">

  <!-- SEO Meta Tags -->
  <meta name="keywords" content="IT services, cloud solutions, cybersecurity, digital transformation, small business technology">
  <meta name="author" content="">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="/resources/">

  <!-- Open Graph -->
  <meta property="og:title" content="Free IT Resources | ">
  <meta property="og:description" content="Download free IT guides, checklists, and whitepapers. Expert insights on cloud migration, cybersecurity, digital transformation, and technology best practices.">
  <meta property="og:type" content="article">
  <meta property="og:url" content="/resources/">
  <meta property="og:site_name" content="">
  <meta property="og:image" content="/images/og-default.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Free IT Resources | ">
  <meta name="twitter:description" content="Download free IT guides, checklists, and whitepapers. Expert insights on cloud migration, cybersecurity, digital transformation, and technology best practices.">
  <meta name="twitter:image" content="/images/og-default.jpg">
  <meta name="twitter:site" content="@nextechsolutions">
  <meta name="twitter:creator" content="@nextechsolutions">

  <!-- Article specific meta tags -->
  

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    
    "name": "Free IT Resources",
    "description": "Download free IT guides, checklists, and whitepapers. Expert insights on cloud migration, cybersecurity, digital transformation, and technology best practices.",
    "url": "/resources/",
    "isPartOf": {
      "@type": "WebSite",
      "name": "",
      "url": ""
    }
    
  }
  </script>

  <!-- Additional structured data for blog posts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://images.pexels.com">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Theme Color -->
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
</head>
<body class="min-h-screen bg-white">
  <header class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <a href="/" class="flex items-center space-x-2">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">N</span>
        </div>
        <span class="text-2xl font-bold text-gray-900"></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        
      </nav>

      <div class="hidden md:flex items-center space-x-4">
        <a
          href="/contact/"
          class="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Get Started
        </a>
      </div>

      <!-- Mobile menu button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden hidden py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        
        <a
          href="/contact/"
          class="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
        >
          Get Started
        </a>
      </div>
    </div>
  </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script> 
  
  <main class="flex-1">
    
<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Free IT Resources
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Download expert guides, checklists, and whitepapers to help you make informed 
          technology decisions and improve your business operations.
        </p>
      </div>
    </div>
  </section>

  <!-- Resource Categories -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Expert Knowledge at Your Fingertips
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Access our library of practical guides and resources designed to help you navigate 
          technology challenges and opportunities.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        

        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">25+</div>
            <p class="text-gray-700 font-medium">Free Resources</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.1s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">10,000+</div>
            <p class="text-gray-700 font-medium">Downloads</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.2s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">50+</div>
            <p class="text-gray-700 font-medium">Pages of Content</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="text-3xl font-bold text-primary-600 mb-2">100%</div>
            <p class="text-gray-700 font-medium">Free Access</p>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Featured Resources -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Featured Resources
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Our most popular and comprehensive guides to help you succeed with technology.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Complete Cloud Migration Guide"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-4 left-4">
                <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Whitepaper
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                  24 pages
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">Complete Cloud Migration Guide</h3>
              <p class="text-gray-600 mb-4 leading-relaxed">Step-by-step guide to planning and executing a successful cloud migration for your business.</p>
              <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-2">What's Included:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Cloud Strategy
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Migration Planning
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Cost Optimization
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Risk Management
                    </li>
                  
                </ul>
              </div>
              <button
                onclick="downloadResource('Complete Cloud Migration Guide', '/downloads/cloud-migration-guide.pdf')"
                class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center justify-center group"
              >
                Download Free
                <svg class="ml-2 w-4 h-4 group-hover:translate-y-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/60504/security-protection-anti-virus-software-60504.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Cybersecurity Checklist for SMEs"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-4 left-4">
                <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Checklist
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                  8 pages
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">Cybersecurity Checklist for SMEs</h3>
              <p class="text-gray-600 mb-4 leading-relaxed">Essential cybersecurity measures every small and medium business should implement immediately.</p>
              <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-2">What's Included:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Security Assessment
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Best Practices
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Compliance
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Incident Response
                    </li>
                  
                </ul>
              </div>
              <button
                onclick="downloadResource('Cybersecurity Checklist for SMEs', '/downloads/cybersecurity-checklist.pdf')"
                class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center justify-center group"
              >
                Download Free
                <svg class="ml-2 w-4 h-4 group-hover:translate-y-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4"></path>
                </svg>
              </button>
            </div>
          </div>
        
          <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <div class="relative h-48 overflow-hidden">
              <img
                src="https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600&amp;h=400&amp;fit=crop"
                alt="Digital Transformation Roadmap"
                class="w-full h-full object-cover"
              />
              <div class="absolute top-4 left-4">
                <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Guide
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                  32 pages
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-3">Digital Transformation Roadmap</h3>
              <p class="text-gray-600 mb-4 leading-relaxed">Comprehensive framework for planning and implementing digital transformation initiatives.</p>
              <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-2">What's Included:</h4>
                <ul class="space-y-1">
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Strategy Development
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Technology Selection
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Change Management
                    </li>
                  
                    <li class="text-sm text-gray-600 flex items-center">
                      <svg class="w-3 h-3 text-success-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      ROI Measurement
                    </li>
                  
                </ul>
              </div>
              <button
                onclick="downloadResource('Digital Transformation Roadmap', '/downloads/digital-transformation-roadmap.pdf')"
                class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center justify-center group"
              >
                Download Free
                <svg class="ml-2 w-4 h-4 group-hover:translate-y-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4"></path>
                </svg>
              </button>
            </div>
          </div>
        
      </div>
    </div>
  </section>

  <!-- All Resources -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Complete Resource Library
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Browse our complete collection of guides, checklists, and templates organized by category.
        </p>
      </div>

      <!-- Resource Categories -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                  </svg>
                
              </div>
              <h3 class="text-lg font-bold text-gray-900">Cloud Solutions</h3>
            </div>
            <ul class="space-y-2">
              
                <li>
                  <button
                    onclick="downloadResource('Cloud Migration Checklist', '/downloads/cloud-migration-checklist.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Cloud Migration Checklist</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Checklist</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Cloud Cost Optimization Guide', '/downloads/cloud-cost-optimization.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Cloud Cost Optimization Guide</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Multi-Cloud Strategy Template', '/downloads/multi-cloud-strategy.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Multi-Cloud Strategy Template</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Template</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Cloud Security Best Practices', '/downloads/cloud-security-practices.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Cloud Security Best Practices</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Whitepaper</span>
                    </div>
                  </button>
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                
              </div>
              <h3 class="text-lg font-bold text-gray-900">Cybersecurity</h3>
            </div>
            <ul class="space-y-2">
              
                <li>
                  <button
                    onclick="downloadResource('Security Assessment Template', '/downloads/security-assessment-template.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Security Assessment Template</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Template</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Incident Response Playbook', '/downloads/incident-response-playbook.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Incident Response Playbook</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Playbook</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Employee Security Training Guide', '/downloads/security-training-guide.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Employee Security Training Guide</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('HIPAA Compliance Checklist', '/downloads/hipaa-compliance-checklist.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">HIPAA Compliance Checklist</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Checklist</span>
                    </div>
                  </button>
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                
              </div>
              <h3 class="text-lg font-bold text-gray-900">Digital Transformation</h3>
            </div>
            <ul class="space-y-2">
              
                <li>
                  <button
                    onclick="downloadResource('Digital Maturity Assessment', '/downloads/digital-maturity-assessment.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Digital Maturity Assessment</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Assessment</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Technology ROI Calculator', '/downloads/technology-roi-calculator.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Technology ROI Calculator</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Tool</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Change Management Framework', '/downloads/change-management-framework.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Change Management Framework</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Framework</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Automation Opportunity Finder', '/downloads/automation-opportunity-finder.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Automation Opportunity Finder</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Tool</span>
                    </div>
                  </button>
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                
              </div>
              <h3 class="text-lg font-bold text-gray-900">IT Management</h3>
            </div>
            <ul class="space-y-2">
              
                <li>
                  <button
                    onclick="downloadResource('IT Budget Planning Template', '/downloads/it-budget-template.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">IT Budget Planning Template</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Template</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Vendor Evaluation Checklist', '/downloads/vendor-evaluation-checklist.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Vendor Evaluation Checklist</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Checklist</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Disaster Recovery Plan Template', '/downloads/disaster-recovery-template.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Disaster Recovery Plan Template</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Template</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('IT Policy Templates', '/downloads/it-policy-templates.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">IT Policy Templates</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Templates</span>
                    </div>
                  </button>
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                
              </div>
              <h3 class="text-lg font-bold text-gray-900">Industry Specific</h3>
            </div>
            <ul class="space-y-2">
              
                <li>
                  <button
                    onclick="downloadResource('Healthcare IT Compliance Guide', '/downloads/healthcare-it-compliance.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Healthcare IT Compliance Guide</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Financial Services Security Framework', '/downloads/financial-security-framework.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Financial Services Security Framework</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Framework</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Manufacturing IoT Implementation Guide', '/downloads/manufacturing-iot-guide.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Manufacturing IoT Implementation Guide</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Retail Technology Trends Report', '/downloads/retail-tech-trends.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Retail Technology Trends Report</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Report</span>
                    </div>
                  </button>
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600 mr-3">
                
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                  </svg>
                
              </div>
              <h3 class="text-lg font-bold text-gray-900">Best Practices</h3>
            </div>
            <ul class="space-y-2">
              
                <li>
                  <button
                    onclick="downloadResource('Remote Work IT Setup Guide', '/downloads/remote-work-it-setup.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Remote Work IT Setup Guide</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Data Backup Best Practices', '/downloads/data-backup-practices.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Data Backup Best Practices</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Network Security Checklist', '/downloads/network-security-checklist.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Network Security Checklist</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Checklist</span>
                    </div>
                  </button>
                </li>
              
                <li>
                  <button
                    onclick="downloadResource('Software License Management Guide', '/downloads/software-license-management.pdf')"
                    class="w-full text-left p-2 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 group-hover:text-primary-600">Software License Management Guide</span>
                      <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Guide</span>
                    </div>
                  </button>
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Newsletter Signup -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Get New Resources First
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Subscribe to our newsletter and be the first to access new guides, checklists, and industry insights.
        </p>
        <form class="max-w-md mx-auto flex flex-col sm:flex-row gap-4">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600"
            required
          >
          <button
            type="submit"
            class="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
          >
            Subscribe
          </button>
        </form>
        <p class="text-sm text-blue-100 mt-4">
          No spam, unsubscribe at any time. We respect your privacy.
        </p>
      </div>
    </div>
  </section>
</div>

<!-- Download Modal -->
<div id="downloadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
  <div class="bg-white rounded-xl p-8 max-w-md mx-4">
    <h3 class="text-2xl font-bold text-gray-900 mb-4">Download Resource</h3>
    <p class="text-gray-600 mb-6">Enter your email to download this free resource. We'll also send you updates on new resources and IT insights.</p>
    <form id="downloadForm">
      <input type="hidden" id="resourceName" name="resource">
      <input type="hidden" id="downloadUrl" name="url">
      <div class="mb-4">
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
        <input
          type="email"
          id="email"
          name="email"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="<EMAIL>"
        >
      </div>
      <div class="mb-6">
        <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company (Optional)</label>
        <input
          type="text"
          id="company"
          name="company"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Your Company"
        >
      </div>
      <div class="flex space-x-4">
        <button
          type="button"
          onclick="closeDownloadModal()"
          class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          Download
        </button>
      </div>
    </form>
  </div>
</div>

<script>
function downloadResource(name, url) {
  document.getElementById('resourceName').value = name;
  document.getElementById('downloadUrl').value = url;
  document.getElementById('downloadModal').classList.remove('hidden');
  document.getElementById('downloadModal').classList.add('flex');
}

function closeDownloadModal() {
  document.getElementById('downloadModal').classList.add('hidden');
  document.getElementById('downloadModal').classList.remove('flex');
}

document.getElementById('downloadForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const resourceName = formData.get('resource');
  const downloadUrl = formData.get('url');
  
  // Here you would typically send the form data to your server
  // For now, we'll just simulate the download
  console.log('Resource download:', resourceName, 'Email:', formData.get('email'));
  
  // Simulate download
  alert('Thank you! Your download will begin shortly. Check your email for additional resources.');
  
  closeDownloadModal();
  
  // Reset form
  this.reset();
});

// Close modal when clicking outside
document.getElementById('downloadModal').addEventListener('click', function(e) {
  if (e.target === this) {
    closeDownloadModal();
  }
});
</script>

  </main>
  
  <footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">N</span>
          </div>
          <span class="text-2xl font-bold"></span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md">
          Empowering SMEs with cutting-edge IT solutions. We transform businesses through 
          technology, delivering innovative services that drive growth and efficiency.
        </p>
        <div class="flex space-x-4">
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span></span>
          </div>
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span></span>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Services</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/services/cloud-solutions" class="hover:text-white transition-colors">Cloud Solutions</a></li>
          <li><a href="/services/cybersecurity" class="hover:text-white transition-colors">Cybersecurity</a></li>
          <li><a href="/services/digital-transformation" class="hover:text-white transition-colors">Digital Transformation</a></li>
          <li><a href="/services/it-consulting" class="hover:text-white transition-colors">IT Consulting</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Company</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
          <li><a href="/portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
          <li><a href="/blog" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-300 text-sm">
        © 2025 . All rights reserved.
      </p>
      <div class="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-300">
        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
      </div>
    </div>
  </div>
</footer>

<!-- Scroll to Top Button -->
<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 hidden"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const scrollToTopButton = document.getElementById('scroll-to-top');
  
  if (scrollToTopButton) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 400) {
        scrollToTopButton.classList.remove('hidden');
      } else {
        scrollToTopButton.classList.add('hidden');
      }
    });
    
    scrollToTopButton.addEventListener('click', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});
</script> 
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  
  
</body>
</html> 