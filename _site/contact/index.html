<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us | </title>
  <meta name="description" content="Get in touch with NexTech Solutions. We&#39;re here to help transform your business with innovative IT solutions.">

  <!-- SEO Meta Tags -->
  <meta name="keywords" content="IT services, cloud solutions, cybersecurity, digital transformation, small business technology">
  <meta name="author" content="">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="/contact/">

  <!-- Open Graph -->
  <meta property="og:title" content="Contact Us | ">
  <meta property="og:description" content="Get in touch with NexTech Solutions. We&#39;re here to help transform your business with innovative IT solutions.">
  <meta property="og:type" content="article">
  <meta property="og:url" content="/contact/">
  <meta property="og:site_name" content="">
  <meta property="og:image" content="/images/og-default.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Contact Us | ">
  <meta name="twitter:description" content="Get in touch with NexTech Solutions. We&#39;re here to help transform your business with innovative IT solutions.">
  <meta name="twitter:image" content="/images/og-default.jpg">
  <meta name="twitter:site" content="@nextechsolutions">
  <meta name="twitter:creator" content="@nextechsolutions">

  <!-- Article specific meta tags -->
  

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    
    "name": "Contact Us",
    "description": "Get in touch with NexTech Solutions. We&#39;re here to help transform your business with innovative IT solutions.",
    "url": "/contact/",
    "isPartOf": {
      "@type": "WebSite",
      "name": "",
      "url": ""
    }
    
  }
  </script>

  <!-- Additional structured data for blog posts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://images.pexels.com">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Theme Color -->
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
</head>
<body class="min-h-screen bg-white">
  <header class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <a href="/" class="flex items-center space-x-2">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">N</span>
        </div>
        <span class="text-2xl font-bold text-gray-900"></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        
      </nav>

      <div class="hidden md:flex items-center space-x-4">
        <a
          href="/contact/"
          class="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Get Started
        </a>
      </div>

      <!-- Mobile menu button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden hidden py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        
        <a
          href="/contact/"
          class="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
        >
          Get Started
        </a>
      </div>
    </div>
  </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script> 
  
  <main class="flex-1">
    
<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Contact Us
        </h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
          Ready to transform your business? Get in touch with our experts and 
          discover how we can help you achieve your technology goals.
        </p>
      </div>
    </div>
  </section>

  <!-- Contact Form & Info -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
        <!-- Contact Form -->
        <div class="animate-slide-up">
          <h2 class="text-3xl font-bold text-gray-900 mb-8">
            Send us a message
          </h2>
          
          <form id="contact-form" class="space-y-6" action="#" method="POST">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                  placeholder="John"
                />
              </div>
              <div>
                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                  placeholder="Doe"
                />
              </div>
            </div>
            
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                Company
              </label>
              <input
                type="text"
                id="company"
                name="company"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                placeholder="Your Company"
              />
            </div>
            
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                placeholder="+****************"
              />
            </div>
            
            <div>
              <label for="service" class="block text-sm font-medium text-gray-700 mb-2">
                Service Interest
              </label>
              <select
                id="service"
                name="service"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              >
                <option value="">Select a service</option>
                <option value="cloud-solutions">Cloud Solutions</option>
                <option value="cybersecurity">Cybersecurity</option>
                <option value="digital-transformation">Digital Transformation</option>
                <option value="it-consulting">IT Consulting</option>
                <option value="managed-services">Managed Services</option>
                <option value="custom-development">Custom Development</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                Message *
              </label>
              <textarea
                id="message"
                name="message"
                rows="6"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
                placeholder="Tell us about your project and how we can help..."
              ></textarea>
            </div>
            
            <div class="flex items-start">
              <input
                type="checkbox"
                id="consent"
                name="consent"
                required
                class="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label for="consent" class="ml-3 text-sm text-gray-600">
                I agree to the <a href="#" class="text-primary-600 hover:text-primary-700">Privacy Policy</a> 
                and consent to being contacted by NexTech Solutions regarding my inquiry. *
              </label>
            </div>
            
            <button
              type="submit"
              class="w-full bg-primary-600 text-white py-4 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-all duration-300 flex items-center justify-center group"
            >
              Send Message
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
            </button>
          </form>
        </div>

        <!-- Contact Information -->
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <h2 class="text-3xl font-bold text-gray-900 mb-8">
            Get in touch
          </h2>
          
          <div class="space-y-8">
            <!-- Office Address -->
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Office Address</h3>
                <p class="text-gray-600"></p>
              </div>
            </div>

            <!-- Phone -->
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Phone</h3>
                <p class="text-gray-600"></p>
              </div>
            </div>

            <!-- Email -->
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Email</h3>
                <p class="text-gray-600"></p>
              </div>
            </div>

            <!-- Business Hours -->
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Business Hours</h3>
                <p class="text-gray-600"></p>
              </div>
            </div>
          </div>

          <!-- Social Links -->
          <div class="mt-12">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Follow Us</h3>
            <div class="flex space-x-4">
              <a href="" class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-primary-600 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <a href="" class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-primary-600 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
              <a href="" class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-primary-600 hover:text-white transition-colors">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Frequently Asked Questions
        </h2>
        <p class="text-xl text-gray-600">
          Quick answers to common questions about our services
        </p>
      </div>

      <div class="space-y-6">
        

        
          <div class="bg-white rounded-lg shadow-md animate-scale-in" style="animation-delay: 0s;">
            <button class="faq-button w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none" data-target="faq-1">
              <span class="text-lg font-semibold text-gray-900">How quickly can you respond to IT emergencies?</span>
              <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div id="faq-1" class="faq-content hidden px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">We provide 24/7 emergency support with response times of 15 minutes for critical issues and 2 hours for standard support requests.</p>
            </div>
          </div>
        
          <div class="bg-white rounded-lg shadow-md animate-scale-in" style="animation-delay: 0.1s;">
            <button class="faq-button w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none" data-target="faq-2">
              <span class="text-lg font-semibold text-gray-900">Do you work with small businesses?</span>
              <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div id="faq-2" class="faq-content hidden px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">Absolutely! We specialize in providing enterprise-level IT solutions tailored specifically for small and medium enterprises (SMEs).</p>
            </div>
          </div>
        
          <div class="bg-white rounded-lg shadow-md animate-scale-in" style="animation-delay: 0.2s;">
            <button class="faq-button w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none" data-target="faq-3">
              <span class="text-lg font-semibold text-gray-900">What&#39;s included in your managed services?</span>
              <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div id="faq-3" class="faq-content hidden px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">Our managed services include 24/7 monitoring, proactive maintenance, help desk support, security management, backup services, and performance optimization.</p>
            </div>
          </div>
        
          <div class="bg-white rounded-lg shadow-md animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <button class="faq-button w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none" data-target="faq-4">
              <span class="text-lg font-semibold text-gray-900">How do you ensure data security?</span>
              <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div id="faq-4" class="faq-content hidden px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">We implement multi-layered security measures including encryption, firewalls, intrusion detection, regular security audits, and compliance with industry standards like SOC 2 and HIPAA.</p>
            </div>
          </div>
        
          <div class="bg-white rounded-lg shadow-md animate-scale-in" style="animation-delay: 0.4s;">
            <button class="faq-button w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none" data-target="faq-5">
              <span class="text-lg font-semibold text-gray-900">Can you help with cloud migration?</span>
              <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div id="faq-5" class="faq-content hidden px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">Yes, we provide comprehensive cloud migration services including assessment, planning, execution, and ongoing optimization for AWS, Azure, and Google Cloud platforms.</p>
            </div>
          </div>
        
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Get Started?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Don't wait for technology problems to slow down your business.
          Contact us today for a free consultation and discover how we can help.
        </p>
        <a
          href="tel:"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Call Now: 
          <svg class="ml-2 w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Contact form handling
  const contactForm = document.getElementById('contact-form');

  contactForm.addEventListener('submit', function(e) {
    e.preventDefault();

    // Basic form validation
    const requiredFields = contactForm.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        isValid = false;
        field.classList.add('border-red-500');
      } else {
        field.classList.remove('border-red-500');
      }
    });

    if (isValid) {
      // Here you would typically send the form data to your backend
      alert('Thank you for your message! We\'ll get back to you within 24 hours.');
      contactForm.reset();
    } else {
      alert('Please fill in all required fields.');
    }
  });

  // FAQ accordion functionality
  const faqButtons = document.querySelectorAll('.faq-button');

  faqButtons.forEach(button => {
    button.addEventListener('click', function() {
      const targetId = this.getAttribute('data-target');
      const content = document.getElementById(targetId);
      const icon = this.querySelector('.faq-icon');

      // Close all other FAQs
      faqButtons.forEach(otherButton => {
        if (otherButton !== this) {
          const otherTargetId = otherButton.getAttribute('data-target');
          const otherContent = document.getElementById(otherTargetId);
          const otherIcon = otherButton.querySelector('.faq-icon');

          otherContent.classList.add('hidden');
          otherIcon.classList.remove('rotate-180');
        }
      });

      // Toggle current FAQ
      content.classList.toggle('hidden');
      icon.classList.toggle('rotate-180');
    });
  });
});
</script>

  </main>
  
  <footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">N</span>
          </div>
          <span class="text-2xl font-bold"></span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md">
          Empowering SMEs with cutting-edge IT solutions. We transform businesses through 
          technology, delivering innovative services that drive growth and efficiency.
        </p>
        <div class="flex space-x-4">
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span></span>
          </div>
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span></span>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Services</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/services/cloud-solutions" class="hover:text-white transition-colors">Cloud Solutions</a></li>
          <li><a href="/services/cybersecurity" class="hover:text-white transition-colors">Cybersecurity</a></li>
          <li><a href="/services/digital-transformation" class="hover:text-white transition-colors">Digital Transformation</a></li>
          <li><a href="/services/it-consulting" class="hover:text-white transition-colors">IT Consulting</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Company</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
          <li><a href="/portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
          <li><a href="/blog" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-300 text-sm">
        © 2025 . All rights reserved.
      </p>
      <div class="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-300">
        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
      </div>
    </div>
  </div>
</footer>

<!-- Scroll to Top Button -->
<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 hidden"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const scrollToTopButton = document.getElementById('scroll-to-top');
  
  if (scrollToTopButton) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 400) {
        scrollToTopButton.classList.remove('hidden');
      } else {
        scrollToTopButton.classList.add('hidden');
      }
    });
    
    scrollToTopButton.addEventListener('click', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});
</script> 
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  
    <script src="/js/contact-form.js"></script>
  
  
</body>
</html> 