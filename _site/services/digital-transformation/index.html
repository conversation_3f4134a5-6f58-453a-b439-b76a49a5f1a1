<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Digital Transformation Services | </title>
  <meta name="description" content="Modernize your operations with cutting-edge digital technologies. Process automation, digital strategy, and technology integration for competitive advantage.">

  <!-- SEO Meta Tags -->
  <meta name="keywords" content="IT services, cloud solutions, cybersecurity, digital transformation, small business technology">
  <meta name="author" content="">
  <meta name="robots" content="index, follow">
  <link rel="canonical" href="/services/digital-transformation/">

  <!-- Open Graph -->
  <meta property="og:title" content="Digital Transformation Services | ">
  <meta property="og:description" content="Modernize your operations with cutting-edge digital technologies. Process automation, digital strategy, and technology integration for competitive advantage.">
  <meta property="og:type" content="article">
  <meta property="og:url" content="/services/digital-transformation/">
  <meta property="og:site_name" content="">
  <meta property="og:image" content="/images/og-default.jpg">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">
  <meta property="og:locale" content="en_US">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Digital Transformation Services | ">
  <meta name="twitter:description" content="Modernize your operations with cutting-edge digital technologies. Process automation, digital strategy, and technology integration for competitive advantage.">
  <meta name="twitter:image" content="/images/og-default.jpg">
  <meta name="twitter:site" content="@nextechsolutions">
  <meta name="twitter:creator" content="@nextechsolutions">

  <!-- Article specific meta tags -->
  

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    
    "name": "Digital Transformation Services",
    "description": "Modernize your operations with cutting-edge digital technologies. Process automation, digital strategy, and technology integration for competitive advantage.",
    "url": "/services/digital-transformation/",
    "isPartOf": {
      "@type": "WebSite",
      "name": "",
      "url": ""
    }
    
  }
  </script>

  <!-- Additional structured data for blog posts -->
  

  <!-- Styles -->
  <link rel="stylesheet" href="/index.css">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://images.pexels.com">

  <!-- Favicon and App Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
  <link rel="manifest" href="/site.webmanifest">

  <!-- Theme Color -->
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
</head>
<body class="min-h-screen bg-white">
  <header class="bg-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <a href="/" class="flex items-center space-x-2">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-xl">N</span>
        </div>
        <span class="text-2xl font-bold text-gray-900"></span>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex space-x-8">
        
      </nav>

      <div class="hidden md:flex items-center space-x-4">
        <a
          href="/contact/"
          class="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Get Started
        </a>
      </div>

      <!-- Mobile menu button -->
      <button
        id="mobile-menu-button"
        class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-50"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="md:hidden hidden py-4 border-t border-gray-200">
      <div class="flex flex-col space-y-2">
        
        <a
          href="/contact/"
          class="mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors text-center"
        >
          Get Started
        </a>
      </div>
    </div>
  </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script> 
  
  <main class="flex-1">
    
<div class="animate-fade-in">
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="animate-slide-up">
          <div class="flex items-center mb-6">
            <div class="w-16 h-16 bg-accent-600 rounded-xl flex items-center justify-center text-white mr-4">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold">
              Digital Transformation
            </h1>
          </div>
          <p class="text-xl text-blue-100 mb-8 leading-relaxed">
            Modernize your operations and workflows with cutting-edge digital technologies. 
            We help you transform your business processes and improve efficiency.
          </p>
          <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <a
              href="/contact/"
              class="bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-accent-700 transition-all duration-300 flex items-center justify-center group"
            >
              Start Your Transformation
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a
              href="/portfolio/"
              class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-all duration-300 text-center"
            >
              View Transformations
            </a>
          </div>
        </div>
        <div class="animate-slide-up" style="animation-delay: 0.2s;">
          <img
            src="https://images.pexels.com/photos/3861958/pexels-photo-3861958.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop"
            alt="Digital Transformation"
            class="rounded-2xl shadow-2xl"
          />
        </div>
      </div>
    </div>
  </section>

  <!-- Why Digital Transformation -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Why Digital Transformation Matters
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          In today's competitive landscape, digital transformation is essential for business survival and growth.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        

        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
              
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">40%</div>
            <p class="text-gray-700 font-medium">increase in operational efficiency</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.1s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
              
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
              
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">25%</div>
            <p class="text-gray-700 font-medium">reduction in operational costs</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.2s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
              
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">60%</div>
            <p class="text-gray-700 font-medium">faster time to market</p>
          </div>
        
          <div class="text-center p-6 bg-primary-50 rounded-xl animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
              
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-10 5a7 7 0 1114 0H5z"></path>
                </svg>
              
            </div>
            <div class="text-3xl font-bold text-primary-600 mb-2">85%</div>
            <p class="text-gray-700 font-medium">improvement in customer satisfaction</p>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Our Transformation Services -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Transformation Services
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Comprehensive digital transformation solutions tailored to your industry and business needs.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        

        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Process Automation</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Automate repetitive tasks and workflows to improve efficiency and reduce human error.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Workflow Automation
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Document Processing
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Data Entry Automation
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Approval Workflows
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Digital Strategy Development</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Comprehensive digital strategy aligned with your business goals and market opportunities.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Digital Roadmap
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Technology Assessment
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  ROI Analysis
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Change Management
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">System Integration</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Connect disparate systems and applications for seamless data flow and improved collaboration.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  API Development
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Data Integration
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Legacy System Modernization
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Real-time Synchronization
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Customer Experience Enhancement</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Transform customer interactions with digital touchpoints and personalized experiences.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Customer Portals
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Mobile Applications
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Chatbots &amp; AI
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Omnichannel Support
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Data Analytics &amp; Intelligence</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Transform raw data into actionable insights for better decision-making and business intelligence.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Business Intelligence
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Predictive Analytics
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Real-time Dashboards
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Data Visualization
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Cloud-First Architecture</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">Design and implement cloud-native solutions for scalability, flexibility, and cost optimization.</p>
            <ul class="space-y-2">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Microservices Architecture
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Serverless Computing
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Container Orchestration
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  DevOps Implementation
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Transformation Methodology -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Transformation Methodology
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          A proven approach that ensures successful digital transformation with minimal disruption.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
        

        
          <div class="text-center animate-scale-in" style="animation-delay: 0s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              01
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Assess</h3>
            <p class="text-gray-600 leading-relaxed">Comprehensive analysis of current state, processes, and technology landscape.</p>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.1s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              02
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Strategize</h3>
            <p class="text-gray-600 leading-relaxed">Develop digital transformation roadmap aligned with business objectives.</p>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.2s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              03
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Design</h3>
            <p class="text-gray-600 leading-relaxed">Create detailed solution architecture and implementation plans.</p>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              04
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Implement</h3>
            <p class="text-gray-600 leading-relaxed">Execute transformation in phases with continuous testing and validation.</p>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.4s;">
            <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-6">
              05
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Optimize</h3>
            <p class="text-gray-600 leading-relaxed">Monitor, measure, and continuously improve digital solutions.</p>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Industry Solutions -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Industry-Specific Solutions
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Tailored digital transformation approaches for different industries and business models.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        

        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Manufacturing</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Industry 4.0 solutions with IoT, automation, and predictive maintenance.</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Smart Factory
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Predictive Maintenance
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Supply Chain Optimization
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Quality Management
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.1s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Healthcare</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Digital health solutions for improved patient care and operational efficiency.</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Electronic Health Records
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Telemedicine
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Patient Portals
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Medical IoT
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.2s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Retail</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Omnichannel retail experiences with e-commerce and customer analytics.</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  E-commerce Platforms
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Inventory Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Customer Analytics
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Mobile Commerce
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Financial Services</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Fintech solutions for digital banking and financial technology innovation.</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Digital Banking
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Payment Processing
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Risk Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Regulatory Compliance
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.4s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Education</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">EdTech solutions for modern learning and educational administration.</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Learning Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Virtual Classrooms
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Student Information Systems
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Educational Analytics
                </li>
              
            </ul>
          </div>
        
          <div class="bg-white p-6 rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 animate-scale-in" style="animation-delay: 0.5s;">
            <h3 class="text-xl font-bold text-gray-900 mb-3">Professional Services</h3>
            <p class="text-gray-600 mb-4 leading-relaxed">Digital solutions for service delivery and client relationship management.</p>
            <ul class="space-y-1">
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Client Portals
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Project Management
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Time Tracking
                </li>
              
                <li class="text-sm text-gray-600 flex items-center">
                  <svg class="w-3 h-3 text-primary-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Document Management
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Technology Stack -->
  <section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Our Technology Stack
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Cutting-edge technologies and platforms that power successful digital transformations.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        

        
          <div class="text-center animate-scale-in" style="animation-delay: 0s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Cloud Platforms</h3>
            <ul class="space-y-2">
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  AWS
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Microsoft Azure
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Google Cloud
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Kubernetes
                </li>
              
            </ul>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.1s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Development</h3>
            <ul class="space-y-2">
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  React/Angular
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Node.js
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Python
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Microservices
                </li>
              
            </ul>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.2s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Data &amp; Analytics</h3>
            <ul class="space-y-2">
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Power BI
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Tableau
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Apache Spark
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Machine Learning
                </li>
              
            </ul>
          </div>
        
          <div class="text-center animate-scale-in" style="animation-delay: 0.30000000000000004s;">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Automation</h3>
            <ul class="space-y-2">
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  RPA Tools
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Zapier
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Microsoft Power Automate
                </li>
              
                <li class="bg-gray-50 px-3 py-2 rounded-lg text-sm text-gray-700">
                  Custom APIs
                </li>
              
            </ul>
          </div>
        
      </div>
    </div>
  </section>

  <!-- Investment Options -->
  <section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 animate-slide-up">
        <h2 class="text-4xl font-bold text-gray-900 mb-4">
          Transformation Investment Options
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Flexible engagement models to fit your budget and transformation timeline.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        

        
          <div class="bg-white p-8 rounded-xl border-2 border-gray-200 hover:shadow-lg transition-all duration-300 relative animate-scale-in" style="animation-delay: 0s;">
            
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Assessment &amp; Strategy</h3>
              <div class="text-3xl font-bold text-primary-600 mb-2">Starting at $5,000</div>
              <p class="text-gray-600">Comprehensive digital transformation assessment and strategic roadmap development.</p>
            </div>
            <ul class="space-y-3 mb-8">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Current State Analysis
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Digital Maturity Assessment
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Transformation Roadmap
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  ROI Projections
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Technology Recommendations
                </li>
              
            </ul>
            <a
              href="/contact/"
              class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center block"
            >
              Get Started
            </a>
          </div>
        
          <div class="bg-white p-8 rounded-xl border-2 border-primary-500 shadow-xl hover:shadow-lg transition-all duration-300 relative animate-scale-in" style="animation-delay: 0.1s;">
            
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span class="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
            
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Pilot Implementation</h3>
              <div class="text-3xl font-bold text-primary-600 mb-2">Starting at $25,000</div>
              <p class="text-gray-600">Proof-of-concept implementation to validate transformation approach and benefits.</p>
            </div>
            <ul class="space-y-3 mb-8">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Pilot Project Execution
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Technology Implementation
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Process Optimization
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Success Metrics
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Scaling Recommendations
                </li>
              
            </ul>
            <a
              href="/contact/"
              class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center block"
            >
              Get Started
            </a>
          </div>
        
          <div class="bg-white p-8 rounded-xl border-2 border-gray-200 hover:shadow-lg transition-all duration-300 relative animate-scale-in" style="animation-delay: 0.2s;">
            
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Full Transformation</h3>
              <div class="text-3xl font-bold text-primary-600 mb-2">Custom Pricing</div>
              <p class="text-gray-600">Complete digital transformation program with ongoing support and optimization.</p>
            </div>
            <ul class="space-y-3 mb-8">
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  End-to-End Implementation
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Change Management
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Staff Training
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Ongoing Support
                </li>
              
                <li class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 text-success-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Continuous Optimization
                </li>
              
            </ul>
            <a
              href="/contact/"
              class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors text-center block"
            >
              Get Started
            </a>
          </div>
        
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-primary-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="animate-slide-up">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Transform Your Business?
        </h2>
        <p class="text-xl text-blue-100 mb-8 leading-relaxed">
          Start your digital transformation journey with a comprehensive assessment and strategic roadmap.
        </p>
        <a
          href="/contact/"
          class="inline-flex items-center bg-white text-primary-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-300 group"
        >
          Begin Your Transformation
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</div>

  </main>
  
  <footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="col-span-1 md:col-span-2">
        <div class="flex items-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-xl">N</span>
          </div>
          <span class="text-2xl font-bold"></span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md">
          Empowering SMEs with cutting-edge IT solutions. We transform businesses through 
          technology, delivering innovative services that drive growth and efficiency.
        </p>
        <div class="flex space-x-4">
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span></span>
          </div>
          <div class="flex items-center space-x-2 text-gray-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span></span>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Services</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/services/cloud-solutions" class="hover:text-white transition-colors">Cloud Solutions</a></li>
          <li><a href="/services/cybersecurity" class="hover:text-white transition-colors">Cybersecurity</a></li>
          <li><a href="/services/digital-transformation" class="hover:text-white transition-colors">Digital Transformation</a></li>
          <li><a href="/services/it-consulting" class="hover:text-white transition-colors">IT Consulting</a></li>
        </ul>
      </div>

      <div>
        <h3 class="text-lg font-semibold mb-4">Company</h3>
        <ul class="space-y-2 text-gray-300">
          <li><a href="/about" class="hover:text-white transition-colors">About Us</a></li>
          <li><a href="/portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
          <li><a href="/blog" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="/contact" class="hover:text-white transition-colors">Contact</a></li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-300 text-sm">
        © 2025 . All rights reserved.
      </p>
      <div class="flex space-x-6 mt-4 md:mt-0 text-sm text-gray-300">
        <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
        <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
      </div>
    </div>
  </div>
</footer>

<!-- Scroll to Top Button -->
<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 hidden"
>
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
  </svg>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const scrollToTopButton = document.getElementById('scroll-to-top');
  
  if (scrollToTopButton) {
    window.addEventListener('scroll', function() {
      if (window.scrollY > 400) {
        scrollToTopButton.classList.remove('hidden');
      } else {
        scrollToTopButton.classList.add('hidden');
      }
    });
    
    scrollToTopButton.addEventListener('click', function() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }
});
</script> 
  
  <!-- Scripts -->
  <script src="/js/animations.js"></script>
  
  
</body>
</html> 